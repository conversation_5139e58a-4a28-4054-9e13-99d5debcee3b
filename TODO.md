# 📋 Luminara Paper优化集成计划

## 🚀 Paper API & 优化完整集成路线图

### 1.1.0-PRE1 - 核心基础设施 (0001-0050)

#### 配置系统 & 基础架构
- [ ] Paper配置文件系统 (0005-Paper-config-files.patch)
- [ ] Paper命令系统 (0011-Paper-command.patch)
- [ ] Paper指标系统 (0012-Paper-Metrics.patch)
- [ ] Paper插件系统 (0013-Paper-Plugins.patch)
- [ ] Timings v2系统 (0014-Timings-v2.patch)
- [ ] 数据转换器重写 (0015-Rewrite-dataconverter-system.patch)

#### 核心优化
- [ ] Starlight光照引擎 (0016-Starlight.patch)
- [ ] TickThread系统 (0017-Add-TickThread.patch)
- [ ] 区块系统重写 (0019-Rewrite-chunk-system.patch)
- [ ] 并发工具类 (0007-ConcurrentUtil.patch)
- [ ] MC工具类 (0009-MC-Utils.patch)

### 1.1.0-PRE2 - 性能优化核心 (0051-0150)

#### 实体优化
- [ ] 实体激活范围2.0 (0337-Entity-Activation-Range-2.0.patch)
- [ ] 实体追踪优化 (0340-Tracking-Range-Improvements.patch)
- [ ] 实体碰撞上限 (0128-Cap-Entity-Collisions.patch)
- [ ] 实体路径查找优化 (0083-Do-not-load-chunks-for-Pathfinding.patch)
- [ ] 防止实体加载区块 (0257-Prevent-Mob-AI-Rules-from-Loading-Chunks.patch)

#### 区块优化
- [ ] 区块加载优化 (0346-Optimise-getChunkAt-calls-for-loaded-chunks.patch)
- [ ] 区块保存重试 (0064-Chunk-Save-Reattempt.patch)
- [ ] 区块居住时间配置 (0080-Configurable-Chunk-Inhabited-Time.patch)
- [ ] 区块tick迭代优化 (0685-Optimise-chunk-tick-iteration.patch)
- [ ] 区块任务执行优化 (0686-Execute-chunk-tasks-mid-tick.patch)

#### 爆炸优化
- [ ] 爆炸优化 (0040-Optimize-explosions.patch)
- [ ] 爆炸击退禁用 (0041-Disable-explosion-knockback.patch)
- [ ] 爆炸处理死亡实体修复 (0039-Fix-lag-from-explosions-processing-dead-entities.patch)

### 1.1.0-PRE3 - 游戏机制优化 (0151-0300)

#### 生物生成优化
- [ ] 每玩家生物生成 (0342-implement-optional-per-player-mob-spawns.patch)
- [ ] 生物生成距离优化 (0684-Optimize-anyPlayerCloseEnoughForSpawning-to-use-dist.patch)
- [ ] 生物生成器tick速率 (0044-Configurable-mob-spawner-tick-rate.patch)
- [ ] 生物消失距离配置 (0024-Add-configurable-despawn-distances-for-living-entiti.patch)

#### 方块优化
- [ ] 方块状态优化 (0107-Optimise-BlockState-s-hashCode-equals.patch)
- [ ] 方块位置内联优化 (0224-Improve-BlockPosition-inlining.patch, 0690-Manually-inline-methods-in-BlockPosition.patch)
- [ ] 方块物理事件优化 (0078-Only-process-BlockPhysicsEvent-if-a-plugin-has-a-lis.patch)
- [ ] 随机方块tick优化 (0703-Optimise-random-block-ticking.patch)

#### 网络优化
- [ ] 网络管理器优化 (0298-Optimize-Network-Manager-and-add-advanced-packet-sup.patch)
- [ ] 数据包限制器 (0694-Add-packet-limiter-config.patch)
- [ ] 非刷新数据包发送优化 (0704-Optimise-non-flush-packet-sending.patch)
- [ ] 实体追踪数据包合并 (0696-Consolidate-flush-calls-for-entity-tracker-packets.patch)

### 1.1.0-PRE4 - 高级功能 (0301-0500)

#### Anti-Xray系统
- [ ] Anti-Xray实现 (0343-Anti-Xray.patch)
- [ ] 矿物隐藏优化

#### 高级API
- [ ] Brigadier Mojang API (0295-Implement-Brigadier-Mojang-API.patch)
- [ ] 异步命令映射构建 (0294-Async-command-map-building.patch)
- [ ] 高度图API (0308-Add-Heightmap-API.patch)
- [ ] 生物生成器API增强 (0309-Mob-Spawner-API-Enhancements.patch)

#### 世界优化
- [ ] 世界时间更新优化 (0277-Optimize-World-Time-Updates.patch)
- [ ] 世界边界优化 (0118-Bound-Treasure-Maps-to-World-Border.patch)
- [ ] 可配置生成区块保持加载 (0313-Configurable-Keep-Spawn-Loaded-range-per-world.patch)

### 1.1.0-PRE5 - 稳定性与修复 (0501-0750)

#### 稳定性修复
- [ ] 异步调用处理 (0131-Properly-handle-async-calls-to-restart-the-server.patch)
- [ ] AsyncCatcher改进 (0679-Improve-and-expand-AsyncCatcher.patch)
- [ ] 实体位置同步修复 (0785-Fix-Entity-Position-Desync.patch)
- [ ] 游戏配置文件并发修复 (0678-Fix-GameProfileCache-concurrency.patch)

#### 内存优化
- [ ] 共享随机数生成器 (0075-Use-a-Shared-Random-for-Entities.patch)
- [ ] 数据位优化 (0087-Optimize-DataBits.patch)
- [ ] 映射注册表优化 (0246-Optimize-MappedRegistry.patch)
- [ ] 哈希映射调色板优化 (0737-Optimize-HashMapPalette.patch)

#### 高级碰撞优化
- [ ] 单一和多AABB VoxelShapes优化 (0739-Highly-optimise-single-and-multi-AABB-VoxelShapes-an.patch)
- [ ] 玩家移动数据包碰撞检查优化 (0740-Optimise-collision-checking-in-player-move-packet-ha.patch)

### 1.1.0-PRE6 - 现代化功能 (0751-1000+)

#### 现代API
- [ ] 多方块变更API (0767-Multi-Block-Change-API-Implementation.patch)
- [ ] 冻结Tick锁定API (0769-Freeze-Tick-Lock-API.patch)
- [ ] 区块再生API (0775-Implement-regenerateChunk.patch)
- [ ] 自定义药水配方 (0786-Custom-Potion-Mixes.patch)

#### 高级优化
- [ ] 附近玩家查找优化 (0705-Optimise-nearby-player-lookups.patch)
- [ ] 村民AI流优化移除 (0706-Remove-streams-for-villager-AI.patch)
- [ ] Velocity压缩和加密原生支持 (0707-Use-Velocity-compression-and-cipher-natives.patch)
- [ ] 数组支持的同步实体数据 (0982-Array-backed-synched-entity-data.patch)

#### 配置增强
- [ ] 动态配置重载 (0067-Allow-Reloading-of-Custom-Permissions.patch)
- [ ] 区域压缩格式配置 (0991-Configurable-Region-Compression-Format.patch)
- [ ] 实体追踪Y坐标范围配置 (1013-Configurable-entity-tracking-range-by-Y-coordinate.patch)

### 1.1.0-FINAL - 完整Paper兼容性

#### 最新功能集成
- [ ] Folia调度器和区域API (0971-Folia-scheduler-and-owned-region-API.patch)
- [ ] 完整的Paper事件系统
- [ ] 所有Paper配置选项
- [ ] Paper命令完整支持

#### 性能调优
- [ ] 最终性能优化调整
- [ ] 内存使用优化
- [ ] 网络性能调优
- [ ] 多线程优化完善

## ✅ 已完成

- [x] 1.0.8-PRE1：支持Velocity Modern转发（Port Mohist and PCF）
- [x] 1.0.8-PRE2：并入MPEM的部分优化项
- [x] 1.0.8-PRE2：支持Adventure库 (0010-Adventure.patch)
- [x] 1.0.8-PRE3：使用Paper方法优化初始化世界的速度
- [x] 1.0.8-RELEASE：更多i18n（打算用AI，我很懒）
- [x] 基础Adventure API集成
- [x] 基础chunk优化
- [x] 基础实体优化
- [x] Velocity Modern转发支持