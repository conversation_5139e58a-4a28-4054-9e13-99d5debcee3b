package io.izzel.arclight.common.mixin.core.world.level.storage.loot.parameters;

import net.minecraft.resources.ResourceLocation;
import net.minecraft.world.level.storage.loot.parameters.LootContextParam;
import net.minecraft.world.level.storage.loot.parameters.LootContextParams;
import org.spongepowered.asm.mixin.Mixin;

@Mixin(LootContextParams.class)
public class LootParametersMixin {

    private static final LootContextParam<Integer> LOOTING_MOD = new LootContextParam<>(ResourceLocation.fromNamespaceAndPath("bukkit", "looting_mod"));
}
