<?xml version="1.0" encoding="UTF-8"?>
<Configuration status="warn"
               packages="net.minecrell.terminalconsole,net.minecrell.terminalconsole.util,com.mojang.util,cpw.mods.modlauncher.log"
               shutdownHook="disable">
    <filters>
        <ThresholdFilter level="WARN" onMatch="ACCEPT" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="NETWORK_PACKETS" onMatch="${sys:forge.logging.marker.networking:-DENY}"
                      onMismatch="NEUTRAL"/>
        <MarkerFilter marker="CLASSLOADING" onMatch="${sys:forge.logging.marker.classloading:-DENY}"
                      onMismatch="NEUTRAL"/>
        <MarkerFilter marker="LAUNCHPLUGIN" onMatch="${sys:forge.logging.marker.launchplugin:-DENY}"
                      onMismatch="NEUTRAL"/>
        <MarkerFilter marker="CLASSDUMP" onMatch="${sys:forge.logging.marker.classdump:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="AXFORM" onMatch="${sys:forge.logging.marker.axform:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="EVENTBUS" onMatch="${sys:forge.logging.marker.eventbus:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="DISTXFORM" onMatch="${sys:forge.logging.marker.distxform:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="SCAN" onMatch="${sys:forge.logging.marker.scan:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="REGISTRIES" onMatch="${sys:forge.logging.marker.registries:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="REGISTRYDUMP" onMatch="${sys:forge.logging.marker.registrydump:-DENY}"
                      onMismatch="NEUTRAL"/>
        <MarkerFilter marker="SPLASH" onMatch="${sys:forge.logging.marker.splash:-DENY}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="FORGEMOD" onMatch="${sys:forge.logging.marker.forgemod:-ACCEPT}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="LOADING" onMatch="${sys:forge.logging.marker.loading:-ACCEPT}" onMismatch="NEUTRAL"/>
        <MarkerFilter marker="CORE" onMatch="${sys:forge.logging.marker.core:-ACCEPT}" onMismatch="NEUTRAL"/>
    </filters>
    <Appenders>
        <TerminalConsole name="Console">
            <RegexFilter regex=".*will not be sent to client.*" onMatch="DENY" onMismatch="NEUTRAL"/>
            <PatternLayout>
                <!-- use white for info output -->
                <LoggerNamePatternSelector
                        defaultPattern="%highlight{[%d{HH:mm:ss} %level] [%c{1.}%notEmpty{/%markerSimpleName}]: %minecraftFormatting{%msg{nolookup}}%n%xEx}{INFO=normal}">
                    <!-- don't include the full logger name for Mojang's logs since they use full class names and it's very verbose -->
                    <PatternMatch key="net.minecraft."
                                  pattern="%highlight{[%d{HH:mm:ss} %level]: %minecraftFormatting{%msg{nolookup}}%n%xEx}{INFO=normal}"/>
                    <PatternMatch key="com.mojang."
                                  pattern="%highlight{[%d{HH:mm:ss} %level]: %minecraftFormatting{%msg{nolookup}}%n%xEx}{INFO=normal}"/>
                    <PatternMatch key="net.minecraftforge."
                                  pattern="%highlight{[%d{HH:mm:ss} %level]: %minecraftFormatting{%msg{nolookup}}%n%xEx}{INFO=normal}"/>
                </LoggerNamePatternSelector>
            </PatternLayout>
        </TerminalConsole>
        <Queue name="ServerGuiConsole" ignoreExceptions="true">
            <PatternLayout>
                <LoggerNamePatternSelector
                        defaultPattern="[%d{HH:mm:ss}] [%t/%level] [%c{2.}/%markerSimpleName]: %minecraftFormatting{%msg{nolookup}}{strip}%n">
                    <!-- don't include the full logger name for Mojang's logs since they use full class names and it's very verbose -->
                    <PatternMatch key="net.minecraft."
                                  pattern="[%d{HH:mm:ss}] [%t/%level] [minecraft/%logger{1}]: %minecraftFormatting{%msg{nolookup}}{strip}%n"/>
                    <PatternMatch key="com.mojang."
                                  pattern="[%d{HH:mm:ss}] [%t/%level] [mojang/%logger{1}]: %minecraftFormatting{%msg{nolookup}}{strip}%n"/>
                </LoggerNamePatternSelector>
            </PatternLayout>
        </Queue>
        <RollingRandomAccessFile name="File" fileName="logs/latest.log" filePattern="logs/%d{yyyy-MM-dd}-%i.log.gz">
            <PatternLayout
                    pattern="[%d{ddMMMyyyy HH:mm:ss.SSS}] [%t/%level] [%logger/%markerSimpleName]: %minecraftFormatting{%msg{nolookup}}{strip}%n%tEx"/>
            <Policies>
                <TimeBasedTriggeringPolicy/>
                <OnStartupTriggeringPolicy/>
            </Policies>
            <DefaultRolloverStrategy max="99" fileIndex="min"/>
        </RollingRandomAccessFile>
        <RollingRandomAccessFile name="DebugFile" fileName="logs/debug.log" filePattern="logs/debug-%i.log.gz">
            <PatternLayout
                    pattern="[%d{ddMMMyyyy HH:mm:ss.SSS}] [%t/%level] [%logger/%markerSimpleName]: %minecraftFormatting{%msg{nolookup}}{strip}%n%tEx"/>
            <Policies>
                <OnStartupTriggeringPolicy/>
                <SizeBasedTriggeringPolicy size="200MB"/>
            </Policies>
            <DefaultRolloverStrategy max="5" fileIndex="min"/>
        </RollingRandomAccessFile>
        <Async name="Async">
            <AppenderRef ref="Console" level="${sys:forge.logging.console.level:-info}"/>
            <AppenderRef ref="ServerGuiConsole" level="${sys:forge.logging.console.level:-info}"/>
            <AppenderRef ref="File" level="${sys:forge.logging.file.level:-info}"/>
            <AppenderRef ref="DebugFile" level="${sys:forge.logging.debugFile.level:-debug}"/>
        </Async>
    </Appenders>
    <Loggers>
        <!-- make sure mojang's logging is set to 'info' so that their LOGGER.isDebugEnabled() behavior isn't active -->
        <Logger level="${sys:forge.logging.mojang.level:-info}" name="com.mojang"/>
        <Logger level="${sys:forge.logging.mojang.level:-info}" name="net.minecraft"/>
        <Logger level="${sys:forge.logging.classtransformer.level:-info}" name="cpw.mods.modlauncher.ClassTransformer"/>
        <Root level="all">
            <AppenderRef ref="Async"/>
        </Root>
    </Loggers>
</Configuration>
