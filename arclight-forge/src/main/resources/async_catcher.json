{"net/minecraft/server/level/ServerLevel": {"m_8872_(Lnet/minecraft/world/entity/Entity;)Z": "entity add", "m_142646_()Lnet/minecraft/world/level/entity/LevelEntityGetter;": "chunk entity get"}, "net/minecraft/server/level/ServerLevel$EntityCallbacks": {"m_141985_(Lnet/minecraft/world/entity/Entity;)V": "entity register", "m_141981_(Lnet/minecraft/world/entity/Entity;)V": "entity unregister"}, "net/minecraft/server/level/ChunkMap$TrackedEntity": {"m_140485_(Lnet/minecraft/server/level/ServerPlayer;)V": "player tracker clear", "m_140497_(Lnet/minecraft/server/level/ServerPlayer;)V": "player tracker update"}, "net/minecraft/world/level/block/state/BlockBehaviour": {"m_6807_(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/Level;Lnet/minecraft/core/BlockPos;Lnet/minecraft/world/level/block/state/BlockState;Z)V": "block place", "m_6810_(Lnet/minecraft/world/level/block/state/BlockState;Lnet/minecraft/world/level/Level;Lnet/minecraft/core/BlockPos;Lnet/minecraft/world/level/block/state/BlockState;Z)V": "block remove"}, "net/minecraft/world/entity/LivingEntity": {"m_147207_(Lnet/minecraft/world/effect/MobEffectInstance;Lnet/minecraft/world/entity/Entity;)Z": "effect add"}, "net/minecraft/server/level/ChunkMap": {"m_140199_(Lnet/minecraft/world/entity/Entity;)V": "entity track", "m_140331_(Lnet/minecraft/world/entity/Entity;)V": "entity untrack"}, "net/minecraft/world/level/storage/loot/LootTable": {"m_79123_(Lnet/minecraft/world/Container;Lnet/minecraft/world/level/storage/loot/LootContext;)V": "loot generate"}, "net/minecraftforge/network/NetworkHooks": {"openGui(Lnet/minecraft/server/level/ServerPlayer;Lnet/minecraft/world/MenuProvider;Ljava/util/function/Consumer;)V": "open gui"}}