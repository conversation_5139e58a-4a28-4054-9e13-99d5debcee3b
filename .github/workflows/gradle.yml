# This workflow will build a Java project with <PERSON>rad<PERSON>
# For more information see: https://help.github.com/actions/language-and-framework-guides/building-and-testing-java-with-gradle

name: Build

on:
  push:
    branches:
      - '**'

jobs:
  build:

    runs-on: ubuntu-latest

    steps:
      - uses: actions/checkout@v2
      - name: Set up JDK 17
        uses: actions/setup-java@v1
        with:
          java-version: '17'
      - name: Cache Gradle User Files
        uses: actions/cache@v4
        with:
          path: ~/.gradle
          key: ${{ runner.os }}-gradle-user-home
      - name: Cache Gradle Files
        uses: actions/cache@v4
        with:
          path: ./.gradle
          key: ${{ runner.os }}-gradle-file
      - name: Grant execute permission for gradlew
        run: chmod +x gradlew
      - name: Build
        run: |
          ./gradlew cleanBuild remapSpigotJar idea --no-daemon -i --stacktrace --refresh-dependencies
          ./gradlew build collect --no-daemon -i --stacktrace
      - name: Upload Artifact
        uses: actions/upload-artifact@v4
        with:
          name: Lu<PERSON>ra
          path: ./build/libs/*.jar

