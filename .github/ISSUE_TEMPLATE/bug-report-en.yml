name: 🐞 Bug Report
description: English template for reporting a bug
labels: [ "Triage" ]

body:
  - type: markdown
    attributes:
      value: |
        Read the [FAQ](https://github.com/IzzelAliz/Arclight/wiki/FAQ) first!

        Report with [latest development version](https://ci.appveyor.com/project/IzzelAliz/arclight-18/build/artifacts), your issue is likely to be fixed.

        Make sure you have searched issues and no similar is present.

  - type: checkboxes
    id: preliminary
    attributes:
      label: I have confirmed that ...
      options:
        - label: Arclight is up to date
        - label: all dependencies are installed
          required: true
        - label: all plugins and mods are up to date
          required: true
        - label: unable to reproduce in Spigot
          required: true
        - label: unable to reproduce in Forge
          required: true

  - type: input
    id: version
    attributes:
      label: Arclight version
      description: Versions are printed on startup. i.e. arclight-1.15.2-1.0.3-SNAPSHOT-9455d03
    validations:
      required: true

  - type: input
    id: environment
    attributes:
      label: OS & Java versions
      description: Type `java -version` in console
      placeholder: |
        OS: CentOS 7.3 x64, Java: openjdk version "16.0.1" 2021-04-20
    validations:
      required: true

  - type: textarea
    id: related
    attributes:
      label: Plugins and Mods
      description: >-
        Run `/plugins` and `/forge mods` in console.
        <br>Try to remove unrelated plugins and mods before reporting
      placeholder: 'Mods & plugins list here'
      render: 'raw'
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: Description
      description: >-
        Please include as much information as possible. 
        <br>For the description, assume we have no idea how mods work, 
        be as detailed as possible and include a step by step reproduction. 
        <br>It is recommended you try to reproduce the issue you are having yourself with as few mods as possible. 
        <br>The clearer the description, the higher the report processing priority
      placeholder: "Detailed information about what should(not) happen"
    validations:
      required: true

  - type: textarea
    id: step
    attributes:
      label: Step to reproduce
      placeholder: |
        1. Install something...

        2. Join the game...

        3. ....

  - type: textarea
    id: logs
    attributes:
      label: Logs
      description: >-
        Logs can be found in /logs/latest.log
        <br>After server is stopped, drag log file below or upload it to https://paste.ubuntu.com/
        <br>**DO NOT COPY PASTE LOG CONTENT DIRECTLY!**
      placeholder: 'Drag log file here to upload or external pastebin link'

  - type: input
    id: pack
    attributes:
      label: Server pack link (Optional)
      description: If you have too much mods/plugins included and you are not able to minimize the reproducible list, you can upload your server pack to GoogleDrive/Mega.
