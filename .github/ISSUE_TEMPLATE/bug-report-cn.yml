name: 🐞 错误报告表格
description: 通过填写表格来报告错误
labels: [ "Triage" ]

body:
  - type: markdown
    attributes:
      value: |
        报告前先阅读[常见问题](https://github.com/IzzelAliz/Arclight/wiki/FAQ)！
        
        请使用[最新的构建版本](https://ci.appveyor.com/project/IzzelAliz/arclight-18/build/artifacts)报告，你发现的问题很可能已经修复。
        
        在进行错误报告之前，请确保没有类似的现有的报告。

  - type: checkboxes
    id: check
    attributes:
      label: 请确认您已完成以下几件事
      description: 市面上大多数整合包，特别是较老版本（1.12.2）的整合包，其自带的插件都无法在高版本环境下运行
      options:
        - label: 正在使用最新的 Arclight
        - label: 已为所有插件及模组安装依赖
          required: true
        - label: 已为所有插件及模组更新至最新版
          required: true
        - label: 不可在 Spigot 复现
          required: true
        - label: 不可在 Forge 复现
          required: true

  - type: input
    id: version
    attributes:
      label: Arclight 版本
      description: 在 Arclight 启动时会打印版本，如 arclight-1.15.2-1.0.3-SNAPSHOT-9455d03
    validations:
      required: true

  - type: input
    id: environment
    attributes:
      label: Java 版本 | 操作系统
      description: 在命令行中键入 `java -version` 后可以获取版本
      placeholder: |
        系统: CentOS 7.3 x64, Java: openjdk version "16.0.1" 2021-04-20
    validations:
      required: true

  - type: textarea
    id: related
    attributes:
      label: 相关 Mod/插件 的名称及版本
      description: >-
        输入 `/plugins` 和 `/forge mods` 命令可获取相关信息
        <br>如果想要你的问题快速解决，请自行排除无关的模组和插件
        <br>只将有关此问题的模组和插件填入
      placeholder: '在这里插件及模组列表'
      render: 'raw'
    validations:
      required: true

  - type: textarea
    id: description
    attributes:
      label: 错误描述
      description: >-
        请尽可能包含更多描述。
        <br>对于描述，请假设开发者不知道 Mod 是干什么的。
        <br>如果可以的话，在安装尽可能少的 Mod 的情况下复现它。
        <br>越清楚的描述会让该报告处理优先级提高
      placeholder: "描述这个为什么是一个错误，是哪一方面的，以及其他可能需要的信息"
    validations:
      required: true

  - type: textarea
    id: step
    attributes:
      label: 复现步骤
      placeholder: |
        1. 安装 xxx

        2. 进入服务器后 xxxx

        3. ....

  - type: textarea
    id: logs
    attributes:
      label: 报错信息
      description: >-
        日志能在 /logs/latest.log 中找到
        <br>服务器关闭后，将日志文件拖拽至输入框上传
        <br>或上传至 https://paste.ubuntu.com/ 网站，提交后将链接附在下方即可
        <br>如果选择使用其他在线剪贴板，请确保链接可用
        <br>**请勿直接复制粘贴日志文本**
      placeholder: 日志链接，或将日志文件拖到此处上传

  - type: input
    id: pack
    attributes:
      label: 复现用压缩包下载链接(可选)
      description: 如果你有太多的模组或插件，你可以请打包服务端并上传到 OneDrive 上供开发者下载测试
